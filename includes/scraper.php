<?php
/**
 * Scraper functions for Anime Conventions PHP application
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/database.php';

class Scraper {
    private static $useWindows = false;

    /**
     * Set whether to use Windows PowerShell for HTTP requests
     */
    public static function setUseWindows($useWindows) {
        self::$useWindows = $useWindows;
    }
    
    /**
     * Parse event row from HTML table cells
     */
    private static function parseEventRow($cells) {
        if (count($cells) < 3) {
            return null;
        }
        
        $name = trim(strip_tags($cells[0]));
        $dates = trim(strip_tags($cells[1]));
        $location = trim(strip_tags($cells[2]));
        
        return [$name, $dates, $location];
    }
    
    /**
     * Return a DateTime object representing the first day mentioned in dates_str
     */
    private static function firstEventDate($datesStr) {
        global $MONTH_NAMES;
        
        // Look for a 4-digit year
        if (!preg_match('/(20\d{2})/', $datesStr, $match)) {
            return null;
        }
        $year = intval($match[1]);
        
        try {
            $monthDay = trim(explode(',', $datesStr)[0]); // "January 17-19"
            $parts = explode(' ', $monthDay, 2);
            if (count($parts) < 2) {
                return new DateTime("$year-12-31");
            }
            
            $month = $parts[0];
            $dayPart = $parts[1];
            $day = intval(preg_split('/[-–]/', $dayPart)[0]); // handle ranges "17-19"
            
            $monthLower = strtolower($month);
            if (!isset($MONTH_NAMES[$monthLower])) {
                return new DateTime("$year-12-31");
            }
            
            $monthNum = $MONTH_NAMES[$monthLower];
            return new DateTime("$year-$monthNum-$day");
            
        } catch (Exception $e) {
            return new DateTime("$year-12-31");
        }
    }
    
    /**
     * Return a DateTime object representing the last day mentioned in dates_str
     */
    private static function lastEventDate($datesStr) {
        global $MONTH_NAMES;
        
        // Look for a 4-digit year
        if (!preg_match('/(20\d{2})/', $datesStr, $match)) {
            return null;
        }
        $year = intval($match[1]);
        
        try {
            $datesLower = strtolower($datesStr);
            
            // Pattern for "Month Day-Day, Year" (e.g., "July 11-13, 2025")
            if (preg_match('/(\w+)\s+(\d+)[-–](\d+)/', $datesLower, $match)) {
                $monthName = $match[1];
                $endDay = intval($match[3]);
                
                if (isset($MONTH_NAMES[$monthName])) {
                    $month = $MONTH_NAMES[$monthName];
                    return new DateTime("$year-$month-$endDay");
                }
            }
            
            // Pattern for "Month Day - Month Day, Year" (spanning months)
            if (preg_match('/(\w+)\s+(\d+)\s*[-–]\s*(\w+)\s+(\d+)/', $datesLower, $match)) {
                $endMonthName = $match[3];
                $endDay = intval($match[4]);
                
                if (isset($MONTH_NAMES[$endMonthName])) {
                    $endMonth = $MONTH_NAMES[$endMonthName];
                    return new DateTime("$year-$endMonth-$endDay");
                }
            }
            
            // Single day event or fallback - return the first date
            return self::firstEventDate($datesStr);
            
        } catch (Exception $e) {
            return self::firstEventDate($datesStr);
        }
    }
    
    /**
     * Check if an event is currently happening or will happen in the future
     */
    private static function isEventCurrentOrFuture($datesStr) {
        try {
            $endDate = self::lastEventDate($datesStr);
            if (!$endDate) {
                return false;
            }
            
            // Event is current or future if its end date is today or later
            $today = new DateTime();
            return $endDate->format('Y-m-d') >= $today->format('Y-m-d');
            
        } catch (Exception $e) {
            // Fallback to original logic
            $startDate = self::firstEventDate($datesStr);
            return $startDate && $startDate >= new DateTime();
        }
    }
    
    /**
     * Extract convention URL from an individual event page
     */
    private static function extractConventionUrlFromEventPage($eventUrl) {
        try {
            $html = self::fetchUrl($eventUrl);
            
            $dom = new DOMDocument();
            @$dom->loadHTML($html);
            $xpath = new DOMXPath($dom);
            
            // Look for the "Visit Convention Site" button in the box-body div
            $boxBody = $xpath->query("//div[@class='box-body']")->item(0);
            if ($boxBody) {
                // Find the button with "Visit Convention Site" text
                $visitButtons = $xpath->query(".//button[contains(text(), 'Visit Convention Site')]", $boxBody);
                if ($visitButtons->length > 0) {
                    $visitButton = $visitButtons->item(0);
                    $parentLink = $visitButton->parentNode;
                    if ($parentLink->nodeName === 'a' && $parentLink->hasAttributes()) {
                        $hrefAttr = $parentLink->attributes->getNamedItem('href');
                        if ($hrefAttr) {
                            $url = $hrefAttr->nodeValue;
                            // Remove UTM parameters if present
                            if (strpos($url, '?') !== false) {
                                $url = explode('?', $url)[0];
                            }
                            return $url;
                        }
                    }
                }

                // Fallback: look for any link that might be the convention site
                $links = $xpath->query(".//a[@href]", $boxBody);
                foreach ($links as $link) {
                    $hrefAttr = $link->attributes->getNamedItem('href');
                    if ($hrefAttr) {
                        $href = $hrefAttr->nodeValue;
                        // Skip internal links and common tracking domains
                        if (strpos($href, 'http') === 0 &&
                            strpos($href, 'animecons.com') === false &&
                            strpos($href, 'animecons.ca') === false &&
                            strpos($href, 'google.com') === false &&
                            strpos($href, 'media.animecons.com') === false) {
                            // Remove UTM parameters
                            if (strpos($href, '?') !== false) {
                                $href = explode('?', $href)[0];
                            }
                            return $href;
                        }
                    }
                }
            }
            
        } catch (Exception $e) {
            error_log("Failed to extract convention URL from $eventUrl: " . $e->getMessage());
        }
        
        return '';
    }
    
    /**
     * Fetch URL content using Windows PowerShell or cURL
     */
     private static function fetchUrl($url, $isEventbrite = false) {
        // If Windows flag is set, use PowerShell
        if (self::$useWindows) {
            $result = self::fetchUrlWithPowerShell($url);
            if ($result !== null) {
                return $result;
            }
            throw new Exception("Failed to fetch $url using PowerShell");
        }

        // Otherwise use cURL
        if (function_exists('curl_init')) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

            // Enhanced headers for Eventbrite to avoid bot detection
            if ($isEventbrite) {
                curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                    'Accept-Language: en-US,en;q=0.9',
                    'Accept-Encoding: gzip, deflate, br',
                    'DNT: 1',
                    'Connection: keep-alive',
                    'Upgrade-Insecure-Requests: 1',
                    'Sec-Fetch-Dest: document',
                    'Sec-Fetch-Mode: navigate',
                    'Sec-Fetch-Site: none',
                    'Sec-Fetch-User: ?1',
                    'Cache-Control: max-age=0'
                ]);
                curl_setopt($ch, CURLOPT_ENCODING, 'gzip, deflate, br');
                // Add a small delay to appear more human-like
                usleep(rand(500000, 1500000)); // 0.5-1.5 seconds
            } else {
                curl_setopt($ch, CURLOPT_USERAGENT, USER_AGENT);
            }

            $html = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($html !== false && $httpCode == 200) {
                return $html;
            }

            // For Eventbrite, provide more specific error information
            if ($isEventbrite) {
                throw new Exception("Failed to fetch $url - HTTP $httpCode (likely blocked by anti-bot protection)");
            }
        }

        throw new Exception("Failed to fetch $url using cURL");
    }

    /**
     * Fetch URL using PowerShell Invoke-WebRequest
     */
    private static function fetchUrlWithPowerShell($url) {
        try {
            // Properly escape the User-Agent for PowerShell
            $userAgent = str_replace('"', '""', USER_AGENT);
            $escapedUrl = str_replace('"', '""', $url);

            // Use PowerShell Invoke-WebRequest with proper escaping and progress suppression
            $command = 'powershell -Command "$ProgressPreference = \'SilentlyContinue\'; try { $response = Invoke-WebRequest -Uri \"' . $escapedUrl . '\" -UserAgent \"' . $userAgent . '\" -TimeoutSec 30 -UseBasicParsing; $response.Content } catch { Write-Error $_.Exception.Message; exit 1 }"';

            $output = shell_exec($command);
            if ($output !== null && trim($output) !== '' && strpos($output, 'At line:') === false && strpos($output, 'Reading web response') === false) {
                return $output;
            }

            // Fallback to PowerShell with .NET WebClient
            $command = 'powershell -Command "$ProgressPreference = \'SilentlyContinue\'; try { $client = New-Object System.Net.WebClient; $client.Headers.Add(\"User-Agent\", \"' . $userAgent . '\"); $client.DownloadString(\"' . $escapedUrl . '\") } catch { Write-Error $_.Exception.Message; exit 1 }"';

            $output = shell_exec($command);
            if ($output !== null && trim($output) !== '' && strpos($output, 'At line:') === false) {
                return $output;
            }

            return null;

        } catch (Exception $e) {
            error_log("PowerShell fetch failed: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Generic table parser for AnimeCons/FanCons schedule pages
     */
    private static function fetchAnimeConsTable($url, $sourceName, $country) {
        global $US_STATE_ABBR_TO_NAME, $CA_PROVINCE_ABBR_TO_NAME;

        error_log("Fetching $url");

        $html = self::fetchUrl($url);
        
        $dom = new DOMDocument();
        @$dom->loadHTML($html);
        $xpath = new DOMXPath($dom);
        
        $events = [];
        $tables = $xpath->query("//table");
        
        foreach ($tables as $table) {
            $headerRow = $xpath->query(".//tr", $table)->item(0);
            if (!$headerRow) continue;
            
            $headerText = $headerRow->textContent;
            if (strpos($headerText, 'Dates') === false || strpos($headerText, 'Location') === false) {
                continue;
            }
            
            $rows = $xpath->query(".//tr", $table);
            for ($i = 1; $i < $rows->length; $i++) {
                $row = $rows->item($i);
                $cells = $xpath->query(".//td", $row);
                
                if ($cells->length < 3) continue;
                
                $cellTexts = [];
                foreach ($cells as $cell) {
                    $cellTexts[] = $cell->textContent;
                }
                
                $parsed = self::parseEventRow($cellTexts);
                if (!$parsed) continue;
                
                list($name, $dates, $location) = $parsed;
                $startDate = self::firstEventDate($dates);
                
                if ($startDate && self::isEventCurrentOrFuture($dates)) {
                    // Extract province/state from location
                    $provinceState = 'N/A';
                    if (strpos($location, ',') !== false) {
                        $locationParts = explode(',', $location);
                        $provinceState = trim(end($locationParts));
                        
                        // Map US state abbreviations to full names
                        if ($country === 'US' && isset($US_STATE_ABBR_TO_NAME[$provinceState])) {
                            $provinceState = $US_STATE_ABBR_TO_NAME[$provinceState];
                        }
                        // Map CA province abbreviations to full names
                        if ($country === 'CA' && isset($CA_PROVINCE_ABBR_TO_NAME[$provinceState])) {
                            $provinceState = $CA_PROVINCE_ABBR_TO_NAME[$provinceState];
                        }
                    }
                    
                    // Try to find the event page URL to extract convention website
                    $conventionUrl = '';
                    try {
                        $nameCell = $cells->item(0);
                        $eventLinks = $xpath->query(".//a[@href]", $nameCell);
                        if ($eventLinks->length > 0) {
                            $eventLink = $eventLinks->item(0);
                            $hrefAttr = $eventLink->attributes->getNamedItem('href');
                            if ($hrefAttr) {
                                $eventPageUrl = $hrefAttr->nodeValue;

                                // Make sure it's a relative URL and convert to absolute
                                if (strpos($eventPageUrl, '/') === 0) {
                                    $baseDomain = strpos($url, 'animecons.com') !== false ? 'https://animecons.com' : 'https://animecons.ca';
                                    $eventPageUrl = $baseDomain . $eventPageUrl;
                                } elseif (strpos($eventPageUrl, 'http') !== 0) {
                                    $baseDomain = strpos($url, 'animecons.com') !== false ? 'https://animecons.com' : 'https://animecons.ca';
                                    $eventPageUrl = $baseDomain . '/' . $eventPageUrl;
                                }

                                // Extract convention URL from the event page
                                $conventionUrl = self::extractConventionUrlFromEventPage($eventPageUrl);
                                if ($conventionUrl) {
                                    error_log("Found convention URL for $name: $conventionUrl");
                                }
                            }
                        }
                    } catch (Exception $e) {
                            error_log("Failed to extract convention URL for $name: " . $e->getMessage());
                        }
                    
                    $events[] = [
                        'start_date' => $startDate->format('Y-m-d\TH:i:s'),
                        'name' => $name,
                        'dates' => $dates,
                        'location' => $location,
                        'source' => $sourceName,
                        'country' => $country,
                        'province_state' => $provinceState,
                        'url' => $conventionUrl
                    ];
                }
            }
        }
        
        return $events;
    }

    /**
     * Fetch events from AnimeCons.ca
     */
    public static function fetchFromAnimeConsCa() {
        $url = 'https://animecons.ca/events/schedule.php?loc=ca';
        return self::fetchAnimeConsTable($url, 'AnimeCons.ca', 'CA');
    }

    /**
     * Fetch events from AnimeCons.com for the US
     */
    public static function fetchFromAnimeConsUs() {
        $url = 'https://animecons.com/events/schedule.php?loc=us';
        return self::fetchAnimeConsTable($url, 'AnimeCons.com', 'US');
    }

    /**
     * Fetch events from AnimeCons.com for Canada
     */
    public static function fetchFromAnimeConsCom() {
        $url = 'https://animecons.com/events/schedule.php?loc=CA'; // CA == Canada on .com site
        return self::fetchAnimeConsTable($url, 'AnimeCons.com', 'CA');
    }

    /**
     * Try to fetch Eventbrite using headless browser simulation
     */
    private static function fetchEventbriteWithHeadlessBrowser($url) {
        // Try using wget with browser-like headers as a fallback
        $tempFile = tempnam(sys_get_temp_dir(), 'eventbrite_');

        $command = sprintf(
            'wget --quiet --timeout=30 --tries=1 --user-agent=%s --header=%s --header=%s --header=%s --header=%s -O %s %s 2>/dev/null',
            escapeshellarg('Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
            escapeshellarg('Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'),
            escapeshellarg('Accept-Language: en-US,en;q=0.5'),
            escapeshellarg('Accept-Encoding: gzip, deflate'),
            escapeshellarg('Connection: keep-alive'),
            escapeshellarg($tempFile),
            escapeshellarg($url)
        );

        exec($command, $output, $returnCode);

        if ($returnCode === 0 && file_exists($tempFile) && filesize($tempFile) > 0) {
            $content = file_get_contents($tempFile);
            unlink($tempFile);
            if ($content && strlen($content) > 1000) { // Basic sanity check
                return $content;
            }
        }

        if (file_exists($tempFile)) {
            unlink($tempFile);
        }

        throw new Exception("Headless browser simulation failed");
    }

    /**
     * Fetch Eventbrite URL with multiple fallback strategies
     */
    private static function fetchEventbriteWithRetry($url, $maxRetries = 2) {
        $strategies = [
            'curl_enhanced',
            'headless_browser'
        ];

        foreach ($strategies as $strategy) {
            try {
                error_log("Trying Eventbrite fetch strategy: $strategy for $url");

                if ($strategy === 'curl_enhanced') {
                    // Enhanced cURL with better headers
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $url);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 45);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                    curl_setopt($ch, CURLOPT_ENCODING, 'gzip, deflate, br');
                    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');

                    curl_setopt($ch, CURLOPT_HTTPHEADER, [
                        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                        'Accept-Language: en-US,en;q=0.9',
                        'Accept-Encoding: gzip, deflate, br',
                        'Cache-Control: no-cache',
                        'Pragma: no-cache',
                        'Sec-Fetch-Dest: document',
                        'Sec-Fetch-Mode: navigate',
                        'Sec-Fetch-Site: none',
                        'Sec-Fetch-User: ?1',
                        'Upgrade-Insecure-Requests: 1'
                    ]);

                    // Add a delay to appear more human-like
                    usleep(rand(500000, 1500000)); // 0.5-1.5 seconds

                    $html = curl_exec($ch);
                    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);

                    if ($html !== false && $httpCode == 200) {
                        error_log("Successfully fetched Eventbrite URL using $strategy");
                        return $html;
                    }

                    error_log("Strategy $strategy failed with HTTP $httpCode");

                } elseif ($strategy === 'headless_browser') {
                    $html = self::fetchEventbriteWithHeadlessBrowser($url);
                    error_log("Successfully fetched Eventbrite URL using $strategy");
                    return $html;
                }

            } catch (Exception $e) {
                error_log("Strategy $strategy failed: " . $e->getMessage());
                continue;
            }
        }

        throw new Exception("All Eventbrite fetch strategies failed for $url");
    }

    /**
     * Fetch Eventbrite events for a given country from JSON-LD
     */
    public static function fetchFromEventbriteJsonLd($country) {
        global $US_STATE_ABBR_TO_NAME, $CA_PROVINCE_ABBR_TO_NAME;

        if ($country === 'CA') {
            $urls = [
                'https://www.eventbrite.ca/d/canada/anime/',
                'https://www.eventbrite.ca/d/canada/%23cosplay/'
            ];
            $countryCode = 'CA';
        } elseif ($country === 'US') {
            $urls = [
                'https://www.eventbrite.com/d/united-states/anime/',
                'https://www.eventbrite.com/d/united-states/%23cosplay/'
            ];
            $countryCode = 'US';
        } else {
            throw new Exception("country must be 'CA' or 'US'");
        }

        $allEvents = [];

        foreach ($urls as $url) {
            try {
                $html = self::fetchUrl($url, true); // Pass true for Eventbrite

                // Find JSON-LD script tag
                $dom = new DOMDocument();
                @$dom->loadHTML($html);
                $xpath = new DOMXPath($dom);

                $scripts = $xpath->query("//script[@type='application/ld+json']");
                $jsonData = null;

                foreach ($scripts as $script) {
                    $content = $script->textContent;
                    $data = json_decode($content, true);
                    if ($data && isset($data['itemListElement'])) {
                        $jsonData = $data;
                        break;
                    }
                }

                if (!$jsonData) {
                    error_log("No JSON-LD event data found on Eventbrite $country page: $url");
                    continue;
                }

                $events = [];
                foreach ($jsonData['itemListElement'] as $item) {
                    $evt = $item['item'];

                    // Parse start and end date
                    try {
                        $startDt = new DateTime($evt['startDate']);
                    } catch (Exception $e) {
                        $startDt = new DateTime('2099-12-31');
                    }

                    $endDt = null;
                    if (isset($evt['endDate'])) {
                        try {
                            $endDt = new DateTime($evt['endDate']);
                        } catch (Exception $e) {
                            $endDt = null;
                        }
                    }

                    // Format dates as 'Month D-D, YYYY' or 'Month D, YYYY'
                    if ($endDt && $startDt->format('Y-m-d') === $endDt->format('Y-m-d')) {
                        // Single day event (start == end)
                        $dates = $startDt->format('F j, Y');
                    } elseif ($endDt && $startDt->format('Y') === $endDt->format('Y') && $startDt->format('n') === $endDt->format('n')) {
                        // Same month/year: August 8-10, 2025
                        $dates = $startDt->format('F') . ' ' . $startDt->format('j') . '-' . $endDt->format('j') . ', ' . $startDt->format('Y');
                    } elseif ($endDt && $startDt->format('Y') === $endDt->format('Y')) {
                        // Same year, different months: August 30-September 1, 2025
                        $dates = $startDt->format('F j') . '-' . $endDt->format('F j') . ', ' . $startDt->format('Y');
                    } else {
                        // Single day or fallback
                        $dates = $startDt->format('F j, Y');
                    }

                    $name = $evt['name'] ?? '(No title)';
                    $location = $evt['location']['address']['streetAddress'] ?? '';
                    $city = $evt['location']['address']['addressLocality'] ?? '';
                    $region = $evt['location']['address']['addressRegion'] ?? '';
                    $provinceState = $region ?: 'N/A';

                    // For US, map abbreviation to full state name
                    if ($countryCode === 'US' && isset($US_STATE_ABBR_TO_NAME[$region])) {
                        $provinceState = $US_STATE_ABBR_TO_NAME[$region];
                    }
                    // For CA, map abbreviation to full province name
                    if ($countryCode === 'CA' && isset($CA_PROVINCE_ABBR_TO_NAME[$region])) {
                        $provinceState = $CA_PROVINCE_ABBR_TO_NAME[$region];
                    }

                    $fullLocation = trim("$location, $city, $provinceState", ', ');
                    $eventUrl = $evt['url'] ?? '';

                    $events[] = [
                        'start_date' => $startDt->format('Y-m-d\TH:i:s'),
                        'name' => $name,
                        'dates' => $dates,
                        'location' => $fullLocation,
                        'source' => 'Eventbrite',
                        'country' => $countryCode,
                        'province_state' => $provinceState,
                        'url' => $eventUrl
                    ];
                }

                $allEvents = array_merge($allEvents, $events);
                error_log("Found " . count($events) . " events from Eventbrite $country URL: $url");

            } catch (Exception $e) {
                error_log("Failed to parse JSON-LD event data for $country URL $url: " . $e->getMessage());
                continue;
            }
        }

        error_log("Total events from Eventbrite $country: " . count($allEvents));
        return $allEvents;
    }

    /**
     * Scrape Eventbrite for upcoming Canadian and US anime conventions with fallback
     */
    public static function fetchFromEventbrite() {
        $allEvents = [];

        // Try to fetch Canadian events
        try {
            $caEvents = self::fetchFromEventbriteJsonLd('CA');
            $allEvents = array_merge($allEvents, $caEvents);
            error_log("Successfully fetched " . count($caEvents) . " Canadian Eventbrite events");
        } catch (Exception $e) {
            error_log("Failed to fetch Canadian Eventbrite events: " . $e->getMessage());
            $caEvents = [];
        }

        // Try to fetch US events
        try {
            $usEvents = self::fetchFromEventbriteJsonLd('US');
            $allEvents = array_merge($allEvents, $usEvents);
            error_log("Successfully fetched " . count($usEvents) . " US Eventbrite events");
        } catch (Exception $e) {
            error_log("Failed to fetch US Eventbrite events: " . $e->getMessage());
            $usEvents = [];
        }

        // If both failed, log a warning but don't fail the entire scraper
        if (empty($allEvents)) {
            error_log("WARNING: Eventbrite scraping completely failed - likely due to anti-bot protection. Continuing with other sources.");
        }

        error_log("Total Eventbrite events: CA=" . count($caEvents ?? []) . ", US=" . count($usEvents ?? []));
        return $allEvents;
    }

    /**
     * Normalize event name for better matching
     */
    private static function normalizeEventName($name) {
        // Remove common year suffixes and normalize
        $normalized = preg_replace('/\s+202[0-9]/', '', strtolower($name));
        $normalized = preg_replace('/\s+/', ' ', $normalized);
        return trim($normalized);
    }

    /**
     * Check if two events are likely the same event from different sources
     */
    private static function eventsAreSimilar($evt1, $evt2) {
        // Check if dates match
        if ($evt1['start_date'] !== $evt2['start_date']) {
            return false;
        }

        // Check if normalized names are similar
        $name1 = self::normalizeEventName($evt1['name']);
        $name2 = self::normalizeEventName($evt2['name']);

        // Exact match after normalization
        if ($name1 === $name2) {
            return true;
        }

        // Check if one name contains the other
        if (strpos($name1, $name2) !== false || strpos($name2, $name1) !== false) {
            return true;
        }

        // Check if names are very similar (allowing for minor differences)
        if (strlen($name1) > 10 && strlen($name2) > 10) {
            // If names are long enough, check if they share most words
            $words1 = array_unique(explode(' ', $name1));
            $words2 = array_unique(explode(' ', $name2));

            if (count($words1) >= 2 && count($words2) >= 2) {
                $commonWords = array_intersect($words1, $words2);
                $minWords = min(count($words1), count($words2));
                if (count($commonWords) >= $minWords * 0.7) { // 70% word overlap
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Run all scrapers and merge results, deduplicating by (name, start date) with AnimeCons priority
     */
    public static function aggregateEvents() {
        $allEvents = [];
        $scrapers = [
            'fetchFromAnimeConsCa',
            'fetchFromAnimeConsUs',
            'fetchFromEventbrite',
            // 'fetchFromAnimeConsCom', // Uncomment if needed
        ];

        foreach ($scrapers as $scraperMethod) {
            try {
                error_log("Running scraper: $scraperMethod");
                $events = self::$scraperMethod();
                $allEvents = array_merge($allEvents, $events);
                error_log("Scraper $scraperMethod found " . count($events) . " events");
            } catch (Exception $e) {
                error_log("Scraper $scraperMethod failed: " . $e->getMessage());
            }
        }

        // Improved deduplication with AnimeCons priority
        $uniqueEvents = [];

        // First pass: collect all events by date for easier comparison
        $eventsByDate = [];
        foreach ($allEvents as $evt) {
            $dateKey = substr($evt['start_date'], 0, 10); // Get just the date part
            if (!isset($eventsByDate[$dateKey])) {
                $eventsByDate[$dateKey] = [];
            }
            $eventsByDate[$dateKey][] = $evt;
        }

        // Second pass: deduplicate with priority
        foreach ($eventsByDate as $dateKey => $eventsOnDate) {
            // Sort events by priority (AnimeCons first, then others)
            usort($eventsOnDate, function($a, $b) {
                $priorityA = strpos($a['source'], 'AnimeCons') !== false ? 0 : 1;
                $priorityB = strpos($b['source'], 'AnimeCons') !== false ? 0 : 1;
                return $priorityA - $priorityB;
            });

            foreach ($eventsOnDate as $evt) {
                // Check if this event is similar to any already processed event
                $isDuplicate = false;
                foreach ($uniqueEvents as $existingEvt) {
                    if (self::eventsAreSimilar($evt, $existingEvt)) {
                        // Log which event we're keeping and which we're dropping
                        error_log("Duplicate detected: keeping '{$existingEvt['name']}' ({$existingEvt['source']}) over '{$evt['name']}' ({$evt['source']})");
                        $isDuplicate = true;
                        break;
                    }
                }

                if (!$isDuplicate) {
                    $uniqueEvents[] = $evt;
                }
            }
        }

        // Sort by date
        usort($uniqueEvents, function($a, $b) {
            return strcmp($a['start_date'], $b['start_date']);
        });

        error_log("Total unique events after deduplication: " . count($uniqueEvents));
        return $uniqueEvents;
    }

    /**
     * Main scraper function
     */
    public static function runScraper($initDb = false, $showStats = false, $clearOld = false, $useWindows = false) {
        // Set the Windows flag
        self::setUseWindows($useWindows);
        // Configure error logging
        error_log("Starting scraper run...");
        $startTime = microtime(true);

        // Delete the database before scraping (unless just initializing, stats, or clearing old)
        if (!($initDb || $showStats || $clearOld)) {
            if (file_exists(DB_PATH)) {
                unlink(DB_PATH);
                error_log("Deleted database file: " . DB_PATH);
            }
        }

        // Initialize database
        try {
            Database::initDatabase();
        } catch (Exception $e) {
            error_log("Failed to initialize database: " . $e->getMessage());
            throw $e;
        }

        // Handle special commands
        if ($initDb) {
            error_log("Database initialized successfully");
            return;
        }

        if ($showStats) {
            $stats = Database::getDatabaseStats();
            echo "Database Statistics:\n";
            echo "  Total events: {$stats['total_events']}\n";
            echo "  Events by source: " . json_encode($stats['by_source']) . "\n";
            echo "  Last update: " . ($stats['last_update'] ? $stats['last_update']->format('Y-m-d H:i:s') : 'None') . "\n";
            return;
        }

        if ($clearOld) {
            Database::clearOldEvents();
            return;
        }

        // Run the scraper
        try {
            // Aggregate events from all sources
            $events = self::aggregateEvents();

            if (empty($events)) {
                error_log("No events found from any source");
                return;
            }

            // Save to database
            Database::saveEvents($events);

            // Debug: print number of events in database after saving
            $stats = Database::getDatabaseStats();
            echo "[DEBUG] Events in database after scraping: {$stats['total_events']}\n";

            // Show summary
            $endTime = microtime(true);
            $duration = $endTime - $startTime;

            error_log("Scraper completed successfully in " . number_format($duration, 1) . " seconds");
            error_log("Saved " . count($events) . " events to database");

            // Show breakdown by source and country
            $sourceCounts = [];
            $countryCounts = [];
            foreach ($events as $event) {
                $source = $event['source'];
                $country = $event['country'];
                $sourceCounts[$source] = ($sourceCounts[$source] ?? 0) + 1;
                $countryCounts[$country] = ($countryCounts[$country] ?? 0) + 1;
            }

            foreach ($sourceCounts as $source => $count) {
                error_log("  $source: $count events");
            }

            foreach ($countryCounts as $country => $count) {
                error_log("  $country: $count events");
            }

        } catch (Exception $e) {
            error_log("Scraper failed: " . $e->getMessage());
            throw $e;
        }
    }
}
?>
